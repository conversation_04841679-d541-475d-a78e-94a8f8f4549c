package services

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	. "cx/app/models"
)

// FileUploadService 檔案上傳服務
type FileUploadService struct {
	db         *gorm.DB
	uploadPath string
	maxSize    int64 // 最大檔案大小 (bytes)
}

// NewFileUploadService 建立檔案上傳服務
func NewFileUploadService(db *gorm.DB, uploadPath string, maxSize int64) *FileUploadService {
	return &FileUploadService{
		db:         db,
		uploadPath: uploadPath,
		maxSize:    maxSize,
	}
}

// UploadedFile 上傳檔案資訊
type UploadedFile struct {
	ID           uint   `json:"id"`
	OriginalName string `json:"original_name"`
	FileName     string `json:"file_name"`
	FilePath     string `json:"file_path"`
	FileSize     int64  `json:"file_size"`
	MimeType     string `json:"mime_type"`
	MD5Hash      string `json:"md5_hash"`
	Category     string `json:"category"`
	UploadedAt   string `json:"uploaded_at"`
	URL          string `json:"url"`
}

// UploadFileRequest 上傳檔案請求
type UploadFileRequest struct {
	File        *multipart.FileHeader `form:"file" binding:"required"`
	Category    string                `form:"category"`    // 檔案分類
	Description string                `form:"description"` // 檔案描述
}

// AllowedFileTypes 允許的檔案類型
var AllowedFileTypes = map[string][]string{
	"assignment": {
		".pdf", ".doc", ".docx", ".txt", ".md",
		".jpg", ".jpeg", ".png", ".gif",
		".zip", ".rar", ".7z",
	},
	"image": {
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",
	},
	"document": {
		".pdf", ".doc", ".docx", ".txt", ".md", ".rtf",
	},
	"archive": {
		".zip", ".rar", ".7z", ".tar", ".gz",
	},
}

// UploadFile 上傳檔案
func (s *FileUploadService) UploadFile(ctx *gin.Context, req UploadFileRequest, memberID uint) (*UploadedFile, error) {
	// 檢查檔案大小
	if req.File.Size > s.maxSize {
		return nil, fmt.Errorf("檔案大小超過限制 (%d MB)", s.maxSize/(1024*1024))
	}

	// 檢查檔案類型
	if !s.isAllowedFileType(req.File.Filename, req.Category) {
		return nil, fmt.Errorf("不支援的檔案類型")
	}

	// 開啟檔案
	file, err := req.File.Open()
	if err != nil {
		return nil, fmt.Errorf("開啟檔案失敗: %w", err)
	}
	defer file.Close()

	// 計算 MD5 雜湊
	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return nil, fmt.Errorf("計算檔案雜湊失敗: %w", err)
	}
	md5Hash := fmt.Sprintf("%x", hash.Sum(nil))

	// 檢查是否已存在相同檔案
	var existingFile FileUpload
	if err := s.db.Where("md5_hash = ? AND member_id = ?", md5Hash, memberID).First(&existingFile).Error; err == nil {
		// 檔案已存在，返回現有檔案資訊
		return s.convertToUploadedFile(&existingFile), nil
	}

	// 重置檔案指針
	file.Seek(0, 0)

	// 生成檔案名稱
	fileName := s.generateFileName(req.File.Filename)

	// 建立目錄結構
	categoryPath := filepath.Join(s.uploadPath, req.Category, time.Now().Format("2006/01"))
	if err := os.MkdirAll(categoryPath, 0755); err != nil {
		return nil, fmt.Errorf("建立目錄失敗: %w", err)
	}

	// 檔案完整路徑
	filePath := filepath.Join(categoryPath, fileName)

	// 儲存檔案
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("建立檔案失敗: %w", err)
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		return nil, fmt.Errorf("儲存檔案失敗: %w", err)
	}

	// 儲存檔案記錄到資料庫
	fileUpload := &FileUpload{
		MemberID:     memberID,
		OriginalName: req.File.Filename,
		FileName:     fileName,
		FilePath:     filePath,
		FileSize:     req.File.Size,
		MimeType:     req.File.Header.Get("Content-Type"),
		MD5Hash:      md5Hash,
		Category:     req.Category,
		Description:  req.Description,
	}

	if err := s.db.Create(fileUpload).Error; err != nil {
		// 如果資料庫儲存失敗，刪除已上傳的檔案
		os.Remove(filePath)
		return nil, fmt.Errorf("儲存檔案記錄失敗: %w", err)
	}

	return s.convertToUploadedFile(fileUpload), nil
}

// GetFile 取得檔案資訊
func (s *FileUploadService) GetFile(fileID uint, memberID uint) (*UploadedFile, error) {
	var fileUpload FileUpload
	if err := s.db.Where("id = ? AND member_id = ?", fileID, memberID).First(&fileUpload).Error; err != nil {
		return nil, fmt.Errorf("檔案不存在: %w", err)
	}

	return s.convertToUploadedFile(&fileUpload), nil
}

// GetFilesByMember 取得會員的檔案列表
func (s *FileUploadService) GetFilesByMember(memberID uint, category string, page, limit int) ([]UploadedFile, int64, error) {
	var files []FileUpload
	var total int64

	query := s.db.Model(&FileUpload{}).Where("member_id = ?", memberID)
	if category != "" {
		query = query.Where("category = ?", category)
	}

	// 計算總數
	query.Count(&total)

	// 分頁查詢
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, fmt.Errorf("查詢檔案列表失敗: %w", err)
	}

	// 轉換為回應格式
	result := make([]UploadedFile, len(files))
	for i, file := range files {
		result[i] = *s.convertToUploadedFile(&file)
	}

	return result, total, nil
}

// DeleteFile 刪除檔案
func (s *FileUploadService) DeleteFile(fileID uint, memberID uint) error {
	var fileUpload FileUpload
	if err := s.db.Where("id = ? AND member_id = ?", fileID, memberID).First(&fileUpload).Error; err != nil {
		return fmt.Errorf("檔案不存在: %w", err)
	}

	// 檢查檔案是否被使用
	var submissionCount int64
	s.db.Model(&MemberAssignmentSubmission{}).Where("file_path LIKE ?", "%"+fileUpload.FileName+"%").Count(&submissionCount)
	if submissionCount > 0 {
		return fmt.Errorf("檔案正在使用中，無法刪除")
	}

	// 刪除實體檔案
	if err := os.Remove(fileUpload.FilePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("刪除實體檔案失敗: %w", err)
	}

	// 刪除資料庫記錄
	if err := s.db.Delete(&fileUpload).Error; err != nil {
		return fmt.Errorf("刪除檔案記錄失敗: %w", err)
	}

	return nil
}

// isAllowedFileType 檢查檔案類型是否允許
func (s *FileUploadService) isAllowedFileType(filename, category string) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	allowedTypes, exists := AllowedFileTypes[category]
	if !exists {
		// 如果沒有指定分類，使用作業分類的規則
		allowedTypes = AllowedFileTypes["assignment"]
	}

	for _, allowedExt := range allowedTypes {
		if ext == allowedExt {
			return true
		}
	}

	return false
}

// generateFileName 生成唯一檔案名稱
func (s *FileUploadService) generateFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	timestamp := time.Now().Unix()
	hash := md5.Sum([]byte(fmt.Sprintf("%s_%d", originalName, timestamp)))
	return fmt.Sprintf("%x%s", hash, ext)
}

// convertToUploadedFile 轉換為上傳檔案回應格式
func (s *FileUploadService) convertToUploadedFile(file *FileUpload) *UploadedFile {
	return &UploadedFile{
		ID:           file.ID,
		OriginalName: file.OriginalName,
		FileName:     file.FileName,
		FilePath:     file.FilePath,
		FileSize:     file.FileSize,
		MimeType:     file.MimeType,
		MD5Hash:      file.MD5Hash,
		Category:     file.Category,
		UploadedAt:   file.CreatedAt.Format("2006-01-02 15:04:05"),
		URL:          s.generateFileURL(file.FilePath),
	}
}

// generateFileURL 生成檔案存取 URL
func (s *FileUploadService) generateFileURL(filePath string) string {
	// 移除上傳路徑前綴，生成相對 URL
	relativePath := strings.TrimPrefix(filePath, s.uploadPath)
	relativePath = strings.TrimPrefix(relativePath, "/")
	return fmt.Sprintf("/uploads/%s", relativePath)
}

// CleanupExpiredFiles 清理過期檔案
func (s *FileUploadService) CleanupExpiredFiles(days int) error {
	expiredDate := time.Now().AddDate(0, 0, -days)

	var expiredFiles []FileUpload
	if err := s.db.Where("created_at < ? AND id NOT IN (SELECT DISTINCT file_id FROM member_assignment_submissions WHERE file_id IS NOT NULL)", expiredDate).Find(&expiredFiles).Error; err != nil {
		return fmt.Errorf("查詢過期檔案失敗: %w", err)
	}

	for _, file := range expiredFiles {
		// 刪除實體檔案
		if err := os.Remove(file.FilePath); err != nil && !os.IsNotExist(err) {
			continue // 繼續處理其他檔案
		}

		// 刪除資料庫記錄
		s.db.Delete(&file)
	}

	return nil
}

// GetMaxSize 取得最大檔案大小
func (s *FileUploadService) GetMaxSize() int64 {
	return s.maxSize
}
