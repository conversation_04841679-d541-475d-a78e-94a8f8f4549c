package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"cx/app/controllers"
	. "cx/app/models"
	"cx/app/services"
)

// E2ETestSuite 端到端測試套件
type E2ETestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine

	// 服務
	consultationService *services.ConsultationV2Service
	fileService         *services.FileUploadService

	// 測試資料
	testAdmin  *Admin
	testMember *Member
}

// SetupSuite 設置測試套件
func (suite *E2ETestSuite) SetupSuite() {
	// 連接測試資料庫
	dsn := "root:forwork0926@tcp(localhost:3306)/grace_test?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	suite.Require().NoError(err)

	// 自動遷移
	err = db.AutoMigrate(
		&Admin{},
		&Member{},
		&ConsultationModule{},
		&ConsultationAssignment{},
		&MemberAssignmentSubmission{},
		&ConsultationAppointment{},
		&ConsultationTimeSlot{},
		&ConsultationEligibilityRule{},
		&FileUpload{},
	)
	suite.Require().NoError(err)

	suite.db = db
	suite.consultationService = services.NewConsultationV2Service(db)
	suite.fileService = services.NewFileUploadService(db, "./test_uploads", 10*1024*1024) // 10MB

	// 設置完整的路由
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// 設置中間件（簡化版本）
	suite.router.Use(func(c *gin.Context) {
		// 模擬會員登入
		if memberID := c.Query("member_id"); memberID != "" {
			c.Set("member_id", 1001)
		}
		// 模擬管理員登入
		if adminID := c.Query("admin_id"); adminID != "" {
			c.Set("admin_id", 1)
		}
		c.Next()
	})

	// 設置控制器
	consultationController := controllers.NewConsultationV2Controller(db)
	adminController := controllers.NewAdminConsultationV2Controller(db)
	pageController := controllers.NewConsultationV2PageController(db)

	// 前台 API 路由
	api := suite.router.Group("/api/v2")
	{
		// 諮詢模組相關
		consultations := api.Group("/consultations")
		{
			consultations.GET("/modules", consultationController.GetModules)
			consultations.GET("/:id/assignments", consultationController.GetModuleAssignments)
			consultations.GET("/check-eligibility", consultationController.CheckEligibility)
		}

		// 作業相關
		assignments := api.Group("/assignments")
		{
			assignments.POST("/submit", consultationController.SubmitAssignment)
		}

		// 預約相關
		appointments := api.Group("/appointments")
		{
			appointments.GET("/available-slots", consultationController.GetAvailableSlots)
			appointments.POST("", consultationController.BookAppointment)
			appointments.GET("", consultationController.GetMyAppointments)
		}
	}

	// 後台 API 路由
	admin := suite.router.Group("/api/v2/admin")
	{
		// 總覽
		admin.GET("/dashboard", adminController.GetDashboard)

		// 諮詢模組管理
		modules := admin.Group("/consultations/modules")
		{
			modules.GET("", adminController.GetModules)
			modules.POST("", adminController.CreateModule)
			modules.PUT("/:id", adminController.UpdateModule)
			modules.DELETE("/:id", adminController.DeleteModule)
		}

		// 作業配置管理
		assignments := admin.Group("/assignments")
		{
			assignments.GET("", adminController.GetAssignments)
			assignments.POST("", adminController.CreateAssignment)
		}

		// 作業提交管理
		submissions := admin.Group("/submissions")
		{
			submissions.GET("", adminController.GetSubmissions)
			submissions.PATCH("/:id/review", adminController.ReviewSubmission)
		}

		// 預約管理
		appointments := admin.Group("/appointments")
		{
			appointments.GET("", adminController.GetAppointments)
			appointments.PATCH("/:id/confirm", adminController.ConfirmAppointment)
		}

		// 時段管理
		timeSlots := admin.Group("/time-slots")
		{
			timeSlots.GET("", adminController.GetTimeSlots)
			timeSlots.POST("", adminController.CreateTimeSlot)
		}
	}

	// 前台頁面路由
	pages := suite.router.Group("/consultations/v2")
	{
		pages.GET("/modules", pageController.ShowModules)
		pages.GET("/modules/:id", pageController.ShowModuleDetail)
		pages.GET("/modules/:id/assignments", pageController.ShowAssignmentList)
		pages.GET("/assignments/:id/submit", pageController.ShowAssignmentSubmit)
		pages.GET("/book", pageController.ShowAppointmentBooking)
		pages.GET("/appointments", pageController.ShowMyAppointments)
	}
}

// SetupTest 每個測試前的設置
func (suite *E2ETestSuite) SetupTest() {
	// 清理測試資料
	suite.cleanupTestData()

	// 建立測試管理員
	suite.testAdmin = &Admin{
		ID:        1,
		AdminName: "測試管理員",
		Uid:       "<EMAIL>",
		Status:    "Y",
	}
	suite.db.Create(suite.testAdmin)

	// 建立測試會員
	suite.testMember = &Member{
		ID:     1001,
		Name:   "測試會員",
		Uid:    "<EMAIL>",
		Status: "Y",
		Level:  3,
	}
	suite.db.Create(suite.testMember)
}

// TestCompleteUserJourney 測試完整的使用者旅程
func (suite *E2ETestSuite) TestCompleteUserJourney() {
	suite.T().Log("=== 開始端到端測試：完整使用者旅程 ===")

	// === 管理員設置階段 ===
	suite.T().Log("階段 1: 管理員建立諮詢模組")
	moduleID := suite.createTestModule()

	suite.T().Log("階段 2: 管理員建立作業要求")
	assignmentID := suite.createTestAssignment(moduleID)

	suite.T().Log("階段 3: 管理員建立可用時段")
	suite.createTestTimeSlots()

	// === 會員使用階段 ===
	suite.T().Log("階段 4: 會員瀏覽模組列表")
	suite.memberBrowseModules()

	suite.T().Log("階段 5: 會員查看模組詳情")
	suite.memberViewModuleDetail(moduleID)

	suite.T().Log("階段 6: 會員檢查預約資格")
	suite.memberCheckEligibility(moduleID)

	suite.T().Log("階段 7: 會員提交作業")
	submissionID := suite.memberSubmitAssignment(assignmentID)

	suite.T().Log("階段 8: 管理員審核作業")
	suite.adminReviewAssignment(submissionID)

	suite.T().Log("階段 9: 會員預約諮詢")
	appointmentID := suite.memberBookAppointment(moduleID)

	suite.T().Log("階段 10: 管理員確認預約")
	suite.adminConfirmAppointment(appointmentID)

	suite.T().Log("階段 11: 會員查看預約狀態")
	suite.memberViewAppointments()

	suite.T().Log("=== 端到端測試完成 ===")
}

// createTestModule 建立測試模組
func (suite *E2ETestSuite) createTestModule() uint {
	moduleData := map[string]interface{}{
		"name":        "進階程式設計諮詢",
		"description": "提供進階程式設計技術指導",
		"category":    "advanced_guidance",
		"is_active":   true,
		"sort_order":  1,
	}

	jsonData, _ := json.Marshal(moduleData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/admin/consultations/modules?admin_id=1", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string             `json:"msg"`
		Data ConsultationModule `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("建立模組成功", response.Msg)

	return response.Data.ID
}

// createTestAssignment 建立測試作業
func (suite *E2ETestSuite) createTestAssignment(moduleID uint) uint {
	assignmentData := map[string]interface{}{
		"module_id":   moduleID,
		"title":       "程式設計作業",
		"description": "請完成一個簡單的程式設計專案",
		"is_required": true,
		"sort_order":  1,
	}

	jsonData, _ := json.Marshal(assignmentData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/admin/assignments?admin_id=1", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string                 `json:"msg"`
		Data ConsultationAssignment `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)

	return response.Data.ID
}

// createTestTimeSlots 建立測試時段
func (suite *E2ETestSuite) createTestTimeSlots() {
	tomorrow := time.Now().AddDate(0, 0, 1)

	slotData := map[string]interface{}{
		"date":             tomorrow.Format("2006-01-02"),
		"start_time":       "14:00",
		"end_time":         "15:00",
		"is_available":     true,
		"max_appointments": 1,
	}

	jsonData, _ := json.Marshal(slotData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/admin/time-slots?admin_id=1", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)
}

// memberBrowseModules 會員瀏覽模組列表
func (suite *E2ETestSuite) memberBrowseModules() {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v2/modules", nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string               `json:"msg"`
		Data []ConsultationModule `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("取得模組列表成功", response.Msg)
	suite.Len(response.Data, 1)
}

// memberViewModuleDetail 會員查看模組詳情
func (suite *E2ETestSuite) memberViewModuleDetail(moduleID uint) {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v2/modules/%d/assignments", moduleID), nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)
}

// memberCheckEligibility 會員檢查預約資格
func (suite *E2ETestSuite) memberCheckEligibility(moduleID uint) {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v2/check-eligibility?module_id=%d&member_id=1001", moduleID), nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)
}

// memberSubmitAssignment 會員提交作業
func (suite *E2ETestSuite) memberSubmitAssignment(assignmentID uint) uint {
	submissionData := map[string]interface{}{
		"assignment_id": assignmentID,
		"member_id":     suite.testMember.ID,
		"upload_file":   "/uploads/assignments/test_project.pdf",
		"file_paths":    []string{},
	}

	jsonData, _ := json.Marshal(submissionData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/assignments/submit?member_id=1001", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string                     `json:"msg"`
		Data MemberAssignmentSubmission `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)

	return response.Data.ID
}

// adminReviewAssignment 管理員審核作業
func (suite *E2ETestSuite) adminReviewAssignment(submissionID uint) {
	reviewData := map[string]interface{}{
		"status":      "approved",
		"review_note": "作業完成得很好，符合所有要求。",
	}

	jsonData, _ := json.Marshal(reviewData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PATCH", fmt.Sprintf("/api/v2/admin/submissions/%d/review?admin_id=1", submissionID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)
}

// memberBookAppointment 會員預約諮詢
func (suite *E2ETestSuite) memberBookAppointment(moduleID uint) uint {
	tomorrow := time.Now().AddDate(0, 0, 1)
	appointmentTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 14, 0, 0, 0, time.Local)

	appointmentData := map[string]interface{}{
		"module_id":            moduleID,
		"member_id":            suite.testMember.ID,
		"title":                "程式設計諮詢",
		"description":          "希望討論進階程式設計技術",
		"appointment_datetime": appointmentTime.Format("2006-01-02 15:04:05"),
		"duration_minutes":     60,
	}

	jsonData, _ := json.Marshal(appointmentData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/appointments?member_id=1001", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string                  `json:"msg"`
		Data ConsultationAppointment `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)

	return response.Data.ID
}

// adminConfirmAppointment 管理員確認預約
func (suite *E2ETestSuite) adminConfirmAppointment(appointmentID uint) {
	confirmData := map[string]interface{}{
		"zoom_meeting_id": "123456789",
		"zoom_join_url":   "https://zoom.us/j/123456789",
		"notes":           "預約已確認，請準時參加。",
	}

	jsonData, _ := json.Marshal(confirmData)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PATCH", fmt.Sprintf("/api/v2/admin/appointments/%d/confirm?admin_id=1", appointmentID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)
}

// memberViewAppointments 會員查看預約狀態
func (suite *E2ETestSuite) memberViewAppointments() {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v2/appointments?member_id=1001", nil)
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response struct {
		Msg  string                    `json:"msg"`
		Data []ConsultationAppointment `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("取得預約列表成功", response.Msg)
	suite.Len(response.Data, 1)

	// 檢查預約狀態
	appointment := response.Data[0]
	suite.Equal("confirmed", string(appointment.Status))
	suite.NotEmpty(appointment.ZoomJoinURL)
}

// TestErrorScenarios 測試錯誤場景
func (suite *E2ETestSuite) TestErrorScenarios() {
	suite.T().Log("=== 測試錯誤處理場景 ===")

	// 測試未登入存取
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v2/assignments/submit", nil)
	suite.router.ServeHTTP(w, req)
	// 應該返回錯誤或重定向

	// 測試無效資料提交
	invalidData := map[string]interface{}{
		"assignment_id": "invalid",
		"upload_file":   "",
	}

	jsonData, _ := json.Marshal(invalidData)
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("POST", "/api/v2/assignments/submit?member_id=1001", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	suite.router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)
}

// cleanupTestData 清理測試資料
func (suite *E2ETestSuite) cleanupTestData() {
	tables := []string{
		"consultation_appointments",
		"member_assignment_submissions",
		"consultation_time_slots",
		"consultation_assignments",
		"consultation_modules",
		"file_uploads",
	}

	for _, table := range tables {
		suite.db.Exec(fmt.Sprintf("DELETE FROM %s", table))
	}

	suite.db.Exec("DELETE FROM members WHERE id > 1000")
	suite.db.Exec("DELETE FROM admins WHERE id > 0")
}

// TearDownTest 每個測試後的清理
func (suite *E2ETestSuite) TearDownTest() {
	suite.cleanupTestData()
}

// TearDownSuite 測試套件結束後的清理
func (suite *E2ETestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// TestE2ETestSuite 執行端到端測試套件
func TestE2ETestSuite(t *testing.T) {
	suite.Run(t, new(E2ETestSuite))
}
